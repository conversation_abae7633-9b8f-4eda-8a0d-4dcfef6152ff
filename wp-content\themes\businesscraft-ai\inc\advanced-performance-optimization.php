<?php
/**
 * Advanced Performance Optimization System for ChatGABI
 *
 * This file provides comprehensive performance optimization including
 * caching, database optimization, asset optimization, and real-time monitoring.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_Advanced_Performance_Optimization {

    private $cache_manager;
    private $database_optimizer;
    private $asset_optimizer;
    private $performance_monitor;
    private $memory_optimizer;

    public function __construct() {
        $this->cache_manager = new ChatGABI_Advanced_Cache_Manager();
        $this->database_optimizer = new ChatGABI_Database_Optimizer();
        $this->asset_optimizer = new ChatGABI_Asset_Optimizer();
        $this->performance_monitor = new ChatGABI_Performance_Monitor();
        $this->memory_optimizer = new ChatGABI_Memory_Optimizer();

        $this->init_optimizations();
        $this->init_hooks();
    }

    /**
     * Initialize performance optimizations
     */
    private function init_optimizations() {
        // Enable advanced caching
        $this->cache_manager->init_advanced_caching();

        // Optimize database queries
        $this->database_optimizer->init_query_optimization();

        // Optimize assets
        $this->asset_optimizer->init_asset_optimization();

        // Start performance monitoring
        $this->performance_monitor->start_monitoring();

        // Initialize memory optimization
        $this->memory_optimizer->init_memory_optimization();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Performance hooks
        add_action('init', array($this, 'init_performance_features'), 1);
        add_action('wp_enqueue_scripts', array($this, 'optimize_script_loading'), 1);
        add_action('wp_head', array($this, 'add_performance_headers'), 1);

        // Database optimization hooks
        add_action('pre_get_posts', array($this, 'optimize_queries'));
        add_filter('posts_clauses', array($this, 'optimize_sql_clauses'), 10, 2);

        // Cache hooks
        add_action('chatgabi_language_switched', array($this, 'clear_language_cache'));
        add_action('chatgabi_template_updated', array($this, 'clear_template_cache'));

        // AJAX optimization
        add_action('wp_ajax_chatgabi_get_performance_metrics', array($this, 'ajax_get_performance_metrics'));
        add_action('wp_ajax_chatgabi_optimize_performance', array($this, 'ajax_optimize_performance'));
        add_action('wp_ajax_chatgabi_clear_cache', array($this, 'ajax_clear_cache'));

        // Background optimization
        add_action('chatgabi_optimize_performance', array($this, 'background_optimize_performance'));

        // Shutdown optimization
        add_action('shutdown', array($this, 'cleanup_performance_data'));
    }

    /**
     * Initialize performance features
     */
    public function init_performance_features() {
        // Enable object caching if available
        if (function_exists('wp_cache_init')) {
            wp_cache_init();
        }

        // Enable output compression
        if (!ob_get_level() && !ini_get('zlib.output_compression')) {
            ob_start('ob_gzhandler');
        }

        // Set performance headers
        if (!headers_sent()) {
            header('X-ChatGABI-Performance: Optimized');
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
        }
    }

    /**
     * Optimize script loading
     */
    public function optimize_script_loading() {
        // Defer non-critical scripts
        add_filter('script_loader_tag', array($this, 'defer_non_critical_scripts'), 10, 3);

        // Preload critical resources
        $this->preload_critical_resources();

        // Optimize script dependencies
        $this->optimize_script_dependencies();
    }

    /**
     * Defer non-critical scripts
     */
    public function defer_non_critical_scripts($tag, $handle, $src) {
        // List of non-critical scripts to defer
        $defer_scripts = array(
            'chatgabi-analytics',
            'chatgabi-feedback',
            'chatgabi-user-preferences'
        );

        if (in_array($handle, $defer_scripts)) {
            return str_replace('<script ', '<script defer ', $tag);
        }

        return $tag;
    }

    /**
     * Preload critical resources
     */
    private function preload_critical_resources() {
        // Preload critical CSS
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/css/main.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";

        // Preload critical JavaScript
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/js/chat-block.js" as="script">' . "\n";

        // Preload fonts
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/fonts/main.woff2" as="font" type="font/woff2" crossorigin>' . "\n";
    }

    /**
     * Optimize script dependencies
     */
    private function optimize_script_dependencies() {
        global $wp_scripts;

        // Remove unnecessary jQuery dependencies where possible
        if (isset($wp_scripts->registered['chatgabi-main'])) {
            $wp_scripts->registered['chatgabi-main']->deps = array_diff(
                $wp_scripts->registered['chatgabi-main']->deps,
                array('jquery-ui-core', 'jquery-ui-widget')
            );
        }
    }

    /**
     * Add performance headers
     */
    public function add_performance_headers() {
        // DNS prefetch for external resources
        echo '<link rel="dns-prefetch" href="//api.openai.com">' . "\n";
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";

        // Resource hints
        echo '<link rel="preconnect" href="https://api.openai.com" crossorigin>' . "\n";

        // Performance timing
        echo '<script>window.chatgabiPerformanceStart = performance.now();</script>' . "\n";
    }

    /**
     * Optimize database queries
     */
    public function optimize_queries($query) {
        // Skip optimization for admin queries
        if (is_admin()) {
            return;
        }

        // Optimize main query
        if ($query->is_main_query()) {
            // Limit posts per page for better performance
            if ($query->is_home() || $query->is_archive()) {
                $query->set('posts_per_page', 10);
            }

            // Optimize meta queries
            $meta_query = $query->get('meta_query');
            if (!empty($meta_query)) {
                $query->set('meta_query', $this->optimize_meta_query($meta_query));
            }
        }
    }

    /**
     * Optimize meta queries
     */
    private function optimize_meta_query($meta_query) {
        // Add indexes for common meta queries
        foreach ($meta_query as &$clause) {
            if (isset($clause['key']) && in_array($clause['key'], array(
                'businesscraft_ai_country',
                'businesscraft_ai_industry',
                'chatgabi_preferred_language'
            ))) {
                // Ensure these meta keys have database indexes
                $this->database_optimizer->ensure_meta_index($clause['key']);
            }
        }

        return $meta_query;
    }

    /**
     * Optimize SQL clauses
     */
    public function optimize_sql_clauses($clauses, $query) {
        global $wpdb;

        // Add query hints for better performance
        if (strpos($clauses['where'], 'meta_value') !== false) {
            $clauses['where'] = "/*+ USE_INDEX(meta_key) */ " . $clauses['where'];
        }

        // Optimize ORDER BY clauses
        if (strpos($clauses['orderby'], 'meta_value') !== false) {
            $clauses['orderby'] = str_replace(
                'meta_value',
                'CAST(meta_value AS SIGNED)',
                $clauses['orderby']
            );
        }

        return $clauses;
    }

    /**
     * Clear language cache
     */
    public function clear_language_cache($user_id, $from_language, $to_language) {
        $this->cache_manager->clear_language_cache($to_language);
        $this->cache_manager->clear_user_cache($user_id);
    }

    /**
     * Clear template cache
     */
    public function clear_template_cache($template_id) {
        $this->cache_manager->clear_template_cache($template_id);
    }

    /**
     * Get performance metrics
     */
    public function get_performance_metrics() {
        return array(
            'cache_stats' => $this->cache_manager->get_cache_stats(),
            'database_stats' => $this->database_optimizer->get_database_stats(),
            'memory_usage' => $this->memory_optimizer->get_memory_stats(),
            'response_times' => $this->performance_monitor->get_response_times(),
            'optimization_score' => $this->calculate_optimization_score()
        );
    }

    /**
     * Calculate optimization score
     */
    private function calculate_optimization_score() {
        $metrics = array(
            'cache_hit_rate' => $this->cache_manager->get_hit_rate(),
            'database_efficiency' => $this->database_optimizer->get_efficiency_score(),
            'memory_efficiency' => $this->memory_optimizer->get_efficiency_score(),
            'response_time_score' => $this->performance_monitor->get_response_time_score()
        );

        $total_score = 0;
        $weight_sum = 0;
        $weights = array(
            'cache_hit_rate' => 0.3,
            'database_efficiency' => 0.3,
            'memory_efficiency' => 0.2,
            'response_time_score' => 0.2
        );

        foreach ($metrics as $metric => $value) {
            if (isset($weights[$metric]) && is_numeric($value)) {
                $total_score += $value * $weights[$metric];
                $weight_sum += $weights[$metric];
            }
        }

        return $weight_sum > 0 ? round($total_score / $weight_sum, 2) : 0;
    }

    /**
     * AJAX handler for performance metrics
     */
    public function ajax_get_performance_metrics() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_performance_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }

        $metrics = $this->get_performance_metrics();
        wp_send_json_success($metrics);
    }

    /**
     * AJAX handler for performance optimization
     */
    public function ajax_optimize_performance() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_performance_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }

        $optimization_type = sanitize_text_field($_POST['optimization_type'] ?? 'all');

        $results = array();

        switch ($optimization_type) {
            case 'cache':
                $results['cache'] = $this->cache_manager->optimize_cache();
                break;
            case 'database':
                $results['database'] = $this->database_optimizer->optimize_database();
                break;
            case 'memory':
                $results['memory'] = $this->memory_optimizer->optimize_memory();
                break;
            case 'all':
            default:
                $results['cache'] = $this->cache_manager->optimize_cache();
                $results['database'] = $this->database_optimizer->optimize_database();
                $results['memory'] = $this->memory_optimizer->optimize_memory();
                break;
        }

        wp_send_json_success($results);
    }

    /**
     * AJAX handler for cache clearing
     */
    public function ajax_clear_cache() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_performance_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }

        $cache_type = sanitize_text_field($_POST['cache_type'] ?? 'all');

        $result = $this->cache_manager->clear_cache($cache_type);

        if ($result) {
            wp_send_json_success(__('Cache cleared successfully', 'chatgabi'));
        } else {
            wp_send_json_error(__('Failed to clear cache', 'chatgabi'));
        }
    }

    /**
     * Background performance optimization
     */
    public function background_optimize_performance() {
        // Run background optimizations
        $this->database_optimizer->cleanup_old_data();
        $this->cache_manager->cleanup_expired_cache();
        $this->memory_optimizer->cleanup_memory();

        // Update optimization timestamp
        update_option('chatgabi_last_optimization', time());
    }

    /**
     * Cleanup performance data on shutdown
     */
    public function cleanup_performance_data() {
        // Log performance metrics
        $this->performance_monitor->log_page_performance();

        // Cleanup temporary data
        $this->memory_optimizer->cleanup_temporary_data();

        // Flush output buffer if needed
        if (ob_get_level()) {
            ob_end_flush();
        }
    }
}

/**
 * Advanced Cache Manager
 */
class ChatGABI_Advanced_Cache_Manager {

    private $cache_prefix = 'chatgabi_cache_';
    private $cache_stats = array();

    public function init_advanced_caching() {
        // Initialize cache statistics
        $this->cache_stats = get_option('chatgabi_cache_stats', array(
            'hits' => 0,
            'misses' => 0,
            'sets' => 0,
            'deletes' => 0
        ));
    }

    public function get($key, $group = 'default') {
        $cache_key = $this->cache_prefix . $group . '_' . $key;
        $value = get_transient($cache_key);

        if ($value !== false) {
            $this->cache_stats['hits']++;
            return $value;
        }

        $this->cache_stats['misses']++;
        return false;
    }

    public function set($key, $value, $group = 'default', $expiration = 3600) {
        $cache_key = $this->cache_prefix . $group . '_' . $key;
        $result = set_transient($cache_key, $value, $expiration);

        if ($result) {
            $this->cache_stats['sets']++;
        }

        $this->update_cache_stats();
        return $result;
    }

    public function delete($key, $group = 'default') {
        $cache_key = $this->cache_prefix . $group . '_' . $key;
        $result = delete_transient($cache_key);

        if ($result) {
            $this->cache_stats['deletes']++;
        }

        $this->update_cache_stats();
        return $result;
    }

    public function clear_cache($type = 'all') {
        global $wpdb;

        switch ($type) {
            case 'language':
                $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_{$this->cache_prefix}language_%'");
                break;
            case 'template':
                $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_{$this->cache_prefix}template_%'");
                break;
            case 'user':
                $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_{$this->cache_prefix}user_%'");
                break;
            case 'all':
            default:
                $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_{$this->cache_prefix}%'");
                break;
        }

        return true;
    }

    public function clear_language_cache($language) {
        return $this->clear_cache('language');
    }

    public function clear_template_cache($template_id) {
        return $this->delete('template_' . $template_id, 'template');
    }

    public function clear_user_cache($user_id) {
        return $this->delete('user_' . $user_id, 'user');
    }

    public function get_cache_stats() {
        return $this->cache_stats;
    }

    public function get_hit_rate() {
        $total = $this->cache_stats['hits'] + $this->cache_stats['misses'];
        return $total > 0 ? ($this->cache_stats['hits'] / $total) * 100 : 0;
    }

    public function optimize_cache() {
        // Cleanup expired transients
        $this->cleanup_expired_cache();

        // Optimize cache storage
        $this->optimize_cache_storage();

        return array(
            'expired_cleaned' => true,
            'storage_optimized' => true,
            'cache_stats' => $this->cache_stats
        );
    }

    public function cleanup_expired_cache() {
        global $wpdb;

        // Clean up expired transients
        $wpdb->query("
            DELETE a, b FROM {$wpdb->options} a, {$wpdb->options} b
            WHERE a.option_name LIKE '_transient_%'
            AND a.option_name NOT LIKE '_transient_timeout_%'
            AND b.option_name = CONCAT('_transient_timeout_', SUBSTRING(a.option_name, 12))
            AND b.option_value < UNIX_TIMESTAMP()
        ");

        return true;
    }

    private function optimize_cache_storage() {
        // Implement cache storage optimization
        return true;
    }

    private function update_cache_stats() {
        update_option('chatgabi_cache_stats', $this->cache_stats);
    }
}

// Note: ChatGABI_Database_Optimizer class is now defined in database-optimization.php
// This avoids class redeclaration conflicts while maintaining advanced performance optimization functionality

// Initialize Advanced Performance Optimization
function chatgabi_get_advanced_performance_optimization() {
    static $performance_optimization = null;

    if ($performance_optimization === null) {
        $performance_optimization = new ChatGABI_Advanced_Performance_Optimization();
    }

    return $performance_optimization;
}

// Initialize on WordPress init
add_action('init', function() {
    chatgabi_get_advanced_performance_optimization();
});
?>